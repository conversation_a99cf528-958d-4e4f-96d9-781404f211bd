# 🏝️ Aloha Client 开发指南

## 📋 项目概述

Aloha Client是基于NextChat扩展开发的檀香山本地Agent应用，集成AI对话、地图导航、酒店预订、餐厅推荐等功能。

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Yarn 1.22.19+
- Git

### 安装和运行
```bash
# 安装依赖
yarn install

# 启动开发服务器
yarn dev

# 构建生产版本
yarn build

# 启动生产服务器
yarn start
```

## 📁 项目结构

```
aloha-client/
├── app/                    # Next.js 14 App Router
│   ├── (chat)/            # AI对话模块
│   ├── (map)/             # 地图导航模块
│   ├── (hotels)/          # 酒店预订模块
│   ├── (restaurants)/     # 餐厅推荐模块
│   ├── (attractions)/     # 景点服务模块
│   ├── api/               # API路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 共享组件
│   ├── ui/                # 基础UI组件
│   ├── chat/              # 对话相关组件
│   ├── map/               # 地图相关组件
│   ├── hotels/            # 酒店相关组件
│   ├── restaurants/       # 餐厅相关组件
│   └── attractions/       # 景点相关组件
├── lib/                   # 工具库
│   ├── services/          # API服务
│   └── utils/             # 工具函数
├── hooks/                 # 自定义Hooks
├── store/                 # 状态管理
│   └── slices/            # Zustand状态切片
├── types/                 # TypeScript类型定义
├── public/                # 静态资源
├── docs/                  # 项目文档
└── .env.example           # 环境变量模板
```

## 🔧 开发规范

### 代码风格
- 使用TypeScript严格模式
- 遵循ESLint和Prettier配置
- 组件使用函数式组件和Hooks
- 使用Tailwind CSS进行样式开发

### 命名规范
- **文件名**：使用kebab-case，如`user-profile.tsx`
- **组件名**：使用PascalCase，如`UserProfile`
- **函数名**：使用camelCase，如`getUserProfile`
- **常量名**：使用UPPER_SNAKE_CASE，如`API_BASE_URL`

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🏗️ 架构设计

### 技术栈
- **前端框架**：React 18 + Next.js 14 + TypeScript
- **状态管理**：Zustand
- **UI组件**：Tailwind CSS + Material-UI
- **地图服务**：Google Maps API
- **AI服务**：OpenAI GPT-4, Anthropic Claude

### 模块化设计
每个功能模块都有独立的：
- 页面路由（app目录下的路由组）
- 组件库（components目录下的模块文件夹）
- 状态管理（store/slices目录下的状态切片）
- API服务（lib/services目录下的服务文件）

### 状态管理
使用Zustand进行状态管理，每个模块有独立的状态切片：
```typescript
// store/slices/chatSlice.ts
export const useChatStore = create<ChatState>((set, get) => ({
  messages: [],
  currentSession: null,
  // ... 其他状态和方法
}));
```

## 🔌 API集成

### 环境变量配置
复制`.env.example`为`.env.local`并配置相应的API密钥：
```bash
# AI服务
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 地图服务
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# 第三方服务
BOOKING_API_KEY=your_booking_api_key
YELP_API_KEY=your_yelp_api_key
```

### API服务结构
```typescript
// lib/services/hotelService.ts
export class HotelService {
  async searchHotels(params: SearchParams): Promise<Hotel[]> {
    // 实现酒店搜索逻辑
  }
  
  async bookHotel(booking: HotelBooking): Promise<BookingResult> {
    // 实现酒店预订逻辑
  }
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
yarn test

# 运行测试覆盖率
yarn test:ci

# 监听模式运行测试
yarn test:watch
```

### 测试结构
```
__tests__/
├── components/           # 组件测试
├── hooks/               # Hooks测试
├── lib/                 # 工具函数测试
└── pages/               # 页面测试
```

## 📱 PWA功能

### 配置
项目已配置PWA支持，包括：
- Service Worker
- Web App Manifest
- 离线缓存策略
- 推送通知

### 测试PWA
1. 构建生产版本：`yarn build`
2. 启动生产服务器：`yarn start`
3. 在Chrome中打开开发者工具 > Application > Service Workers
4. 测试离线功能和添加到主屏幕

## 🔗 与HawaiiHub-AI集成

### 集成架构
Aloha Client通过API与HawaiiHub-AI项目集成：
- 共享用户认证和会话管理
- 同步用户偏好和历史记录
- 共享本地知识库和推荐算法

### API接口
```typescript
// 用户数据同步
POST /api/sync/user-preferences
GET /api/sync/user-history

// 智能推荐
POST /api/recommendations/hotels
POST /api/recommendations/restaurants
```

## 🚀 部署

### Vercel部署
1. 连接GitHub仓库到Vercel
2. 配置环境变量
3. 自动部署

### 环境变量配置
在Vercel Dashboard中配置所有必要的环境变量。

## 📊 性能优化

### 代码分割
- 使用Next.js动态导入进行代码分割
- 按路由和组件进行懒加载

### 图片优化
- 使用Next.js Image组件
- 配置图片压缩和WebP格式

### 缓存策略
- API响应缓存
- 静态资源缓存
- Service Worker缓存

## 🐛 调试

### 开发工具
- React Developer Tools
- Redux DevTools（用于Zustand）
- Next.js开发者工具

### 日志记录
```typescript
// 使用统一的日志工具
import { logger } from '@/lib/utils/logger';

logger.info('User action', { userId, action });
logger.error('API error', error);
```

## 📚 学习资源

### 官方文档
- [Next.js 14 文档](https://nextjs.org/docs)
- [React 18 文档](https://react.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

### 相关教程
- Next.js App Router最佳实践
- Zustand状态管理指南
- Google Maps API集成教程

## 🤝 贡献指南

### 开发流程
1. Fork项目仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'feat: add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 代码审查
- 确保所有测试通过
- 遵循代码规范
- 添加必要的文档
- 性能影响评估

---

**Happy Coding! 🏝️**
