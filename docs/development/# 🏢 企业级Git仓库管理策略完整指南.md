### HawaiiHub项目架构建议

基于火鸟门户系统的复杂性，推荐混合架构：

````markdown path=hawaiihub-git-strategy.md mode=EDIT
# HawaiiHub Git仓库架构策略

## 🎯 推荐架构：混合模式

### 核心仓库 (Monorepo)
```
hawaiihub-core/
├── packages/
│   ├── shared/           # 共享组件库
│   ├── ui-components/    # UI组件库
│   ├── utils/           # 工具函数库
│   └── types/           # TypeScript类型定义
├── apps/
│   ├── admin/           # 后台管理系统
│   ├── portal/          # 门户网站
│   └── mobile/          # 移动端应用
└── tools/
    ├── build/           # 构建工具
    ├── eslint-config/   # ESLint配置
    └── prettier-config/ # Prettier配置
```

### 独立仓库 (Multi-repo)
```
hawaiihub-ai/            # AI助手项目
hawaiihub-api/           # API服务
hawaiihub-cms/           # 内容管理系统
hawaiihub-docs/          # 文档站点
hawaiihub-infrastructure/ # 基础设施代码
```

## 📋 分离标准

### 应该合并到Monorepo的项目：
- ✅ 共享相同技术栈
- ✅ 频繁协作开发
- ✅ 版本需要同步
- ✅ 共享大量代码

### 应该独立仓库的项目：
- ✅ 不同技术栈（如Python AI服务）
- ✅ 独立部署周期
- ✅ 不同团队维护
- ✅ 第三方集成项目
````

## 2. 🔗 Git子模块管理

### 子模块使用场景

````bash path=git-submodules-setup.sh mode=EDIT
#!/bin/bash

echo "🔗 设置HawaiiHub Git子模块管理..."

# 1. 在主项目中添加子模块
echo "📦 添加共享组件库子模块..."
git submodule add https://github.com/hawaiihub/shared-components.git packages/shared
git submodule add https://github.com/hawaiihub/ui-kit.git packages/ui-kit
git submodule add https://github.com/hawaiihub/utils.git packages/utils

# 2. 添加AI助手作为子模块
echo "🤖 添加AI助手子模块..."
git submodule add https://github.com/hawaiihub/ai-assistant.git apps/ai-assistant

# 3. 初始化子模块
echo "🚀 初始化所有子模块..."
git submodule init
git submodule update

# 4. 创建子模块管理脚本
cat > scripts/submodule-manager.sh << 'EOF'
#!/bin/bash

case "$1" in
  "update")
    echo "🔄 更新所有子模块到最新版本..."
    git submodule foreach git pull origin main
    git add .
    git commit -m "chore: 更新子模块到最新版本"
    ;;
  "sync")
    echo "🔄 同步子模块配置..."
    git submodule sync --recursive
    git submodule update --init --recursive
    ;;
  "status")
    echo "📊 检查子模块状态..."
    git submodule status --recursive
    ;;
  "clean")
    echo "🧹 清理子模块..."
    git submodule foreach --recursive git clean -fd
    git submodule foreach --recursive git reset --hard
    ;;
  *)
    echo "使用方法: $0 {update|sync|status|clean}"
    exit 1
    ;;
esac
EOF

chmod +x scripts/submodule-manager.sh

echo "✅ 子模块管理设置完成！"
echo "📋 常用命令："
echo "  ./scripts/submodule-manager.sh update  # 更新所有子模块"
echo "  ./scripts/submodule-manager.sh status  # 查看子模块状态"
````

### 子模块版本管理工作流程

````markdown path=submodule-workflow.md mode=EDIT
# Git子模块版本管理工作流程

## 🔄 日常开发流程

### 1. 克隆包含子模块的项目
```bash
# 方法1：克隆时同时初始化子模块
git clone --recursive https://github.com/hawaiihub/main-project.git

# 方法2：先克隆再初始化子模块
git clone https://github.com/hawaiihub/main-project.git
cd main-project
git submodule init
git submodule update
```

### 2. 更新子模块
```bash
# 更新特定子模块
cd packages/shared
git pull origin main
cd ../..
git add packages/shared
git commit -m "feat: 更新共享组件库到v2.1.0"

# 批量更新所有子模块
git submodule foreach git pull origin main
git add .
git commit -m "chore: 更新所有子模块到最新版本"
```

### 3. 子模块开发
```bash
# 在子模块中开发
cd packages/shared
git checkout -b feature/new-component

# 开发完成后推送
git add .
git commit -m "feat: 添加新的按钮组件"
git push origin feature/new-component

# 回到主项目更新子模块引用
cd ../..
git add packages/shared
git commit -m "feat: 集成新的按钮组件"
```

## ⚠️ 注意事项

### 子模块状态检查
```bash
# 检查子模块状态
git submodule status

# 输出示例：
# +a1b2c3d packages/shared (v2.0.0-5-ga1b2c3d)
# -e4f5g6h packages/ui-kit (v1.5.0)
#  i7j8k9l packages/utils (v1.2.0)

# 符号含义：
# + : 子模块有未提交的更改
# - : 子模块未初始化
# 空格: 子模块状态正常
```

### 版本锁定策略
```bash
# 锁定子模块到特定版本
cd packages/shared
git checkout v2.1.0
cd ../..
git add packages/shared
git commit -m "lock: 锁定共享组件库到v2.1.0"
```
````

## 3. 🌊 标准Git工作流程

### GitFlow详细实现

````bash path=gitflow-implementation.sh mode=EDIT
#!/bin/bash

echo "🌊 实施GitFlow工作流程..."

# 1. 初始化GitFlow
git flow init

# 2. 创建功能分支开发流程
echo "📝 创建GitFlow管理脚本..."

cat > scripts/gitflow-manager.sh << 'EOF'
#!/bin/bash

# GitFlow管理脚本

case "$1" in
  "feature")
    case "$2" in
      "start")
        echo "🚀 开始新功能开发: $3"
        git flow feature start $3
        ;;
      "finish")
        echo "✅ 完成功能开发: $3"
        git flow feature finish $3
        ;;
      "publish")
        echo "📤 发布功能分支: $3"
        git flow feature publish $3
        ;;
      *)
        echo "使用方法: $0 feature {start|finish|publish} <feature-name>"
        ;;
    esac
    ;;
  "release")
    case "$2" in
      "start")
        echo "🏷️ 开始发布准备: $3"
        git flow release start $3
        ;;
      "finish")
        echo "🎉 完成版本发布: $3"
        git flow release finish $3
        ;;
      *)
        echo "使用方法: $0 release {start|finish} <version>"
        ;;
    esac
    ;;
  "hotfix")
    case "$2" in
      "start")
        echo "🚨 开始紧急修复: $3"
        git flow hotfix start $3
        ;;
      "finish")
        echo "🔧 完成紧急修复: $3"
        git flow hotfix finish $3
        ;;
      *)
        echo "使用方法: $0 hotfix {start|finish} <hotfix-name>"
        ;;
    esac
    ;;
  "status")
    echo "📊 当前分支状态："
    git branch -a
    echo ""
    echo "📋 最近提交："
    git log --oneline -10
    ;;
  *)
    echo "🌊 GitFlow管理工具"
    echo "使用方法:"
    echo "  $0 feature start <name>     # 开始新功能"
    echo "  $0 feature finish <name>    # 完成功能"
    echo "  $0 release start <version>  # 开始发布"
    echo "  $0 release finish <version> # 完成发布"
    echo "  $0 hotfix start <name>      # 开始热修复"
    echo "  $0 hotfix finish <name>     # 完成热修复"
    echo "  $0 status                   # 查看状态"
    ;;
esac
EOF

chmod +x scripts/gitflow-manager.sh

# 3. 创建分支命名规范
cat > .github/branch-naming-convention.md << 'EOF'
# 分支命名规范

## 🌿 分支类型

### 主要分支
- `main` - 生产环境代码
- `develop` - 开发环境代码

### 功能分支
- `feature/功能描述` - 新功能开发
- `feature/JIRA-123-user-login` - 带工单号的功能

### 发布分支
- `release/v1.2.0` - 版本发布准备

### 修复分支
- `hotfix/紧急修复描述` - 生产环境紧急修复
- `bugfix/bug描述` - 开发环境bug修复

## 📝 命名规则

### 格式要求
- 使用小写字母和连字符
- 避免使用下划线和空格
- 包含简短的描述性信息

### 示例
```bash
# ✅ 正确示例
feature/user-authentication
feature/HAWAII-001-ai-chat-integration
release/v2.1.0
hotfix/login-security-patch
bugfix/mobile-responsive-issue

# ❌ 错误示例
Feature/UserAuth
feature_user_login
feature/用户登录
hotfix/fix
```
EOF

echo "✅ GitFlow工作流程设置完成！"
````

### GitHub Flow vs GitLab Flow

````markdown path=workflow-comparison.md mode=EDIT
# Git工作流程对比

## 🐙 GitHub Flow

### 特点
- 简单直接，只有一个主分支
- 适合持续部署的项目
- 通过Pull Request进行代码审查

### 工作流程
```bash
# 1. 从main分支创建功能分支
git checkout main
git pull origin main
git checkout -b feature/ai-chat-enhancement

# 2. 开发并提交
git add .
git commit -m "feat: 增强AI聊天功能"
git push origin feature/ai-chat-enhancement

# 3. 创建Pull Request
# 在GitHub界面创建PR，进行代码审查

# 4. 合并到main分支
# 通过GitHub界面合并，自动部署
```

## 🦊 GitLab Flow

### 特点
- 结合了GitFlow和GitHub Flow的优点
- 支持环境分支（staging、production）
- 适合复杂的部署流程

### 工作流程
```bash
# 1. 功能开发（同GitHub Flow）
git checkout main
git checkout -b feature/hawaiihub-mobile-app

# 2. 合并到main分支
git checkout main
git merge feature/hawaiihub-mobile-app

# 3. 部署到预发布环境
git checkout pre-production
git merge main

# 4. 部署到生产环境
git checkout production
git merge pre-production
git tag v1.3.0
```

## 🌊 GitFlow

### 特点
- 严格的分支模型
- 适合定期发布的项目
- 支持并行开发多个版本

### HawaiiHub项目实施示例
```bash
# 1. 开发新的AI功能
./scripts/gitflow-manager.sh feature start ai-voice-assistant

# 开发过程...
git add .
git commit -m "feat: 添加语音助手功能"

# 2. 完成功能开发
./scripts/gitflow-manager.sh feature finish ai-voice-assistant

# 3. 准备发布v2.0.0
./scripts/gitflow-manager.sh release start 2.0.0

# 发布准备（更新版本号、文档等）
git add .
git commit -m "chore: 准备v2.0.0发布"

# 4. 完成发布
./scripts/gitflow-manager.sh release finish 2.0.0

# 5. 紧急修复（如果需要）
./scripts/gitflow-manager.sh hotfix start security-patch
# 修复代码...
./scripts/gitflow-manager.sh hotfix finish security-patch
```
````

## 4. 👥 团队协作规范

### 代码审查流程

````markdown path=code-review-process.md mode=EDIT
# HawaiiHub代码审查流程

## 🔍 Pull Request模板

### PR标题格式
```
类型(范围): 简短描述

示例：
feat(ai): 添加语音识别功能
fix(auth): 修复登录超时问题
docs(readme): 更新安装指南
```

### PR描述模板
```markdown
## 📝 变更描述
简要描述本次变更的内容和目的

## 🎯 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (bugfix)
- [ ] 文档更新 (docs)
- [ ] 样式调整 (style)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)
- [ ] 构建相关 (build)

## 🧪 测试情况
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 浏览器兼容性测试

## 📸 截图/演示
（如果是UI变更，请提供截图或GIF）

## 🔗 相关链接
- 相关Issue: #123
- 设计稿: [链接]
- 文档: [链接]

## ✅ 检查清单
- [ ] 代码符合项目规范
- [ ] 已添加必要的注释
- [ ] 已更新相关文档
- [ ] 已考虑向后兼容性
- [ ] 已进行安全检查
```

## 👀 审查标准

### 代码质量检查
```bash
# 1. 自动化检查
npm run lint          # ESLint检查
npm run type-check    # TypeScript类型检查
npm run test          # 运行测试
npm run build         # 构建检查

# 2. 安全检查
npm audit             # 依赖安全检查
npm run security-scan # 代码安全扫描
```

### 审查要点
1. **功能正确性** - 代码是否实现了预期功能
2. **代码质量** - 是否遵循编码规范
3. **性能影响** - 是否有性能问题
4. **安全性** - 是否存在安全漏洞
5. **可维护性** - 代码是否易于理解和维护
6. **测试覆盖** - 是否有足够的测试
````

### 冲突解决标准流程

````bash path=conflict-resolution.sh mode=EDIT
#!/bin/bash

echo "🔧 Git冲突解决标准流程..."

cat > scripts/conflict-resolver.sh << 'EOF'
#!/bin/bash

echo "🚨 Git冲突解决助手"

# 1. 检查冲突状态
echo "📊 检查当前冲突状态..."
git status

echo ""
echo "🔍 冲突文件列表："
git diff --name-only --diff-filter=U

# 2. 提供解决选项
echo ""
echo "🛠️ 冲突解决选项："
echo "1. 手动解决冲突"
echo "2. 使用合并工具"
echo "3. 接受当前分支的更改"
echo "4. 接受传入分支的更改"
echo "5. 中止合并"

read -p "请选择解决方式 (1-5): " choice

case $choice in
  1)
    echo "📝 手动解决冲突..."
    echo "请编辑冲突文件，删除冲突标记："
    echo "<<<<<<< HEAD"
    echo "======="
    echo ">>>>>>> branch-name"
    echo ""
    echo "解决完成后运行："
    echo "git add ."
    echo "git commit -m 'resolve: 解决合并冲突'"
    ;;
  2)
    echo "🔧 启动合并工具..."
    git mergetool
    ;;
  3)
    echo "✅ 接受当前分支的更改..."
    git checkout --ours .
    git add .
    git commit -m "resolve: 接受当前分支的更改"
    ;;
  4)
    echo "📥 接受传入分支的更改..."
    git checkout --theirs .
    git add .
    git commit -m "resolve: 接受传入分支的更改"
    ;;
  5)
    echo "❌ 中止合并..."
    git merge --abort
    ;;
  *)
    echo "❌ 无效选择"
    exit 1
    ;;
esac
EOF

chmod +x scripts/conflict-resolver.sh

# 创建冲突预防指南
cat > docs/conflict-prevention.md << 'EOF'
# Git冲突预防指南

## 🛡️ 预防策略

### 1. 频繁同步
```bash
# 每天开始工作前同步主分支
git checkout main
git pull origin main
git checkout feature/your-branch
git rebase main
```

### 2. 小步提交
```bash
# 避免大量文件的一次性提交
git add specific-file.js
git commit -m "feat: 添加特定功能"

# 而不是
git add .
git commit -m "feat: 添加很多功能"
```

### 3. 合理分工
- 避免多人同时修改同一文件
- 使用组件化开发减少冲突
- 提前沟通重构计划

## 🔧 冲突解决最佳实践

### 1. 理解冲突类型
```bash
# 内容冲突 - 同一行被不同修改
<<<<<<< HEAD
const apiUrl = 'https://api.hawaiihub.net/v1'
=======
const apiUrl = 'https://api.hawaiihub.net/v2'
>>>>>>> feature/api-upgrade

# 解决方案：选择正确的版本或合并两者
const apiUrl = 'https://api.hawaiihub.net/v2'
```

### 2. 使用工具辅助
```bash
# 配置合并工具
git config --global merge.tool vscode
git config --global mergetool.vscode.cmd 'code --wait $MERGED'

# 或使用其他工具
git config --global merge.tool vimdiff
```

### 3. 验证解决结果
```bash
# 解决冲突后必须验证
npm run test
npm run lint
npm run build
```
EOF

echo "✅ 冲突解决流程设置完成！"
echo "📋 使用方法："
echo "  ./scripts/conflict-resolver.sh  # 冲突解决助手"
echo "  查看文档: docs/conflict-prevention.md"
````

### 版本发布和标签管理

````bash path=release-management.sh mode=EDIT
#!/bin/bash

echo "🏷️ 设置版本发布和标签管理..."

cat > scripts/release-manager.sh << 'EOF'
#!/bin/bash

# HawaiiHub版本发布管理器

VERSION_FILE="package.json"
CHANGELOG_FILE="CHANGELOG.md"

# 获取当前版本
get_current_version() {
  if [ -f "$VERSION_FILE" ]; then
    grep '"version"' $VERSION_FILE | sed 's/.*"version": "\(.*\)".*/\1/'
  else
    echo "0.0.0"
  fi
}

# 更新版本号
update_version() {
  local new_version=$1
  if [ -f "$VERSION_FILE" ]; then
    sed -i.bak "s/\"version\": \".*\"/\"version\": \"$new_version\"/" $VERSION_FILE
    rm $VERSION_FILE.bak
  fi
}

# 生成变更日志
generate_changelog() {
  local version=$1
  local date=$(date +"%Y-%m-%d")
  
  echo "## [$version] - $date" >> temp_changelog.md
  echo "" >> temp_changelog.md
  
  # 获取自上次标签以来的提交
  local last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
  if [ -n "$last_tag" ]; then
    git log $last_tag..HEAD --pretty=format:"- %s" >> temp_changelog.md
  else
    git log --pretty=format:"- %s" >> temp_changelog.md
  fi
  
  echo "" >> temp_changelog.md
  echo "" >> temp_changelog.md
  
  # 合并到现有变更日志
  if [ -f "$CHANGELOG_FILE" ]; then
    cat temp_changelog.md $CHANGELOG_FILE > temp_full_changelog.md
    mv temp_full_changelog.md $CHANGELOG_FILE
  else
    mv temp_changelog.md $CHANGELOG_FILE
  fi
  
  rm -f temp_changelog.md
}

# 创建发布
create_release() {
  local version=$1
  local current_version=$(get_current_version)
  
  echo "🏷️ 创建版本发布: $version"
  echo "📊 当前版本: $current_version"
  
  # 确认发布
  read -p "确认发布版本 $version? (y/N): " confirm
  if [[ $confirm != [yY] ]]; then
    echo "❌ 发布已取消"
    exit 1
  fi
  
  # 检查工作区是否干净
  if ! git diff-index --quiet HEAD --; then
    echo "❌ 工作区有未提交的更改，请先提交"
    exit 1
  fi
  
  # 更新版本号
  update_version $version
  
  # 生成变更日志
  generate_changelog $version
  
  # 提交版本更新
  git add $VERSION_FILE $CHANGELOG_FILE
  git commit -m "chore: 发布版本 $version"
  
  # 创建标签
  git tag -a "v$version" -m "版本 $version 发布

$(head -20 $CHANGELOG_FILE)"
  
  # 推送到远程
  git push origin main
  git push origin "v$version"
  
  echo "✅ 版本 $version 发布完成！"
  echo "🔗 GitHub Release: https://github.com/hawaiihub/main-project/releases/tag/v$version"
}

# 主菜单
case "$1" in
  "patch")
    current=$(get_current_version)
    IFS='.' read -ra ADDR <<< "$current"
    new_version="${ADDR[0]}.${ADDR[1]}.$((${ADDR[2]} + 1))"
    create_release $new_version
    ;;
  "minor")
    current=$(get_current_version)
    IFS='.' read -ra ADDR <<< "$current"
    new_version="${ADDR[0]}.$((${ADDR[1]} + 1)).0"
    create_release $new_version
    ;;
  "major")
    current=$(get_current_version)
    IFS='.' read -ra ADDR <<< "$current"
    new_version="$((${ADDR[0]} + 1)).0.0"
    create_release $new_version
    ;;
  "custom")
    if [ -z "$2" ]; then
      echo "❌ 请提供版本号"
      echo "使用方法: $0 custom 1.2.3"
      exit 1
    fi
    create_release $2
    ;;
  "list")
    echo "📋 版本标签列表："
    git tag -l --sort=-version:refname | head -10
    ;;
  "current")
    echo "📊 当前版本: $(get_current_version)"
    ;;
  *)
    echo "🏷️ HawaiiHub版本发布管理器"
    echo ""
    echo "使用方法:"
    echo "  $0 patch     # 补丁版本 (1.0.0 -> 1.0.1)"
    echo "  $0 minor     # 次要版本 (1.0.0 -> 1.1.0)"
    echo "  $0 major     # 主要版本 (1.0.0 -> 2.0.0)"
    echo "  $0 custom X.Y.Z  # 自定义版本"
    echo "  $0 list      # 列出最近的标签"
    echo "  $0 current   # 显示当前版本"
    echo ""
    echo "📊 当前版本: $(get_current_version)"
    ;;
esac
EOF

chmod +x scripts/release-manager.sh

# 创建GitHub Actions工作流
mkdir -p .github/workflows

cat > .github/workflows/release.yml << 'EOF'
name: 🚀 Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🧪 Run tests
        run: npm test

      - name: 🏗️ Build
        run: npm run build

      - name: 📋 Generate Release Notes
        id: release_notes
        run: |
          echo "RELEASE_NOTES<<EOF" >> $GITHUB_OUTPUT
          git log $(git describe --tags --abbrev=0 HEAD^)..HEAD --pretty=format:"- %s" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: 🎉 Create Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          body: |
            ## 🎉 HawaiiHub ${{ github.ref }} 发布

            ### 📝 更新内容
            ${{ steps.release_notes.outputs.RELEASE_NOTES }}

            ### 🔗 相关链接
            - [文档](https://docs.hawaiihub.net)
            - [演示站点](https://demo.hawaiihub.net)
          draft: false
          prerelease: false
EOF

echo "✅ 版本发布管理设置完成！"
echo "📋 使用方法："
echo "  ./scripts/release-manager.sh patch   # 发布补丁版本"
echo "  ./scripts/release-manager.sh minor   # 发布次要版本"
echo "  ./scripts/release-manager.sh major   # 发布主要版本"
````

## 5. 🔐 权限控制和安全策略

````bash path=git-security-setup.sh mode=EDIT
#!/bin/bash

echo "🔐 设置Git安全策略..."

# 1. 分支保护规则
cat > .github/branch-protection.md << 'EOF'
# 分支保护策略

## 🛡️ 主分支保护

### main分支规则
- ✅ 禁止直接推送
- ✅ 要求Pull Request审查
- ✅ 要求状态检查通过
- ✅ 要求分支是最新的
- ✅ 要求管理员遵循规则
- ✅ 限制推送权限

### develop分支规则
- ✅ 要求Pull Request审查
- ✅ 要求至少1个审查者
- ✅ 要求状态检查通过

## 👥 权限分级

### 管理员权限
- 🔧 仓库设置管理
- 🏷️ 创建和删除标签
- 🚀 发布版本
- 👥 管理协作者权限

### 维护者权限
- ✅ 合并Pull Request
- 🔧 管理Issues和Projects
- 📝 编辑仓库描述

### 开发者权限
- 📝 创建Pull Request
- 💬 评论和审查代码
- 🐛 创建Issues

### 只读权限
- 👀 查看代码
- 📥 克隆仓库
- 📋 查看Issues
EOF

# 2. 提交签名设置
cat > scripts/setup-commit-signing.sh << 'EOF'
#!/bin/bash

echo "🔐 设置Git提交签名..."

# 检查GPG密钥
if ! command -v gpg &> /dev/null; then
    echo "❌ GPG未安装，请先安装GPG"
    exit 1
fi

# 列出现有密钥
echo "📋 现有GPG密钥："
gpg --list-secret-keys --keyid-format LONG

read -p "是否需要创建新的GPG密钥? (y/N): " create_key

if [[ $create_key == [yY] ]]; then
    echo "🔑 创建新的GPG密钥..."
    gpg --full-generate-key
fi

# 获取密钥ID
echo "📋 选择要使用的密钥："
gpg --list-secret-keys --keyid-format LONG

read -p "请输入密钥ID (例如: 3AA5C34371567BD2): " key_id

if [ -n "$key_id" ]; then
    # 配置Git使用GPG签名
    git config --global user.signingkey $key_id
    git config --global commit.gpgsign true
    git config --global tag.gpgsign true
    
    # 导出公钥
    echo "📤 导出公钥到GitHub..."
    gpg --armor --export $key_id
    
    echo ""
    echo "✅ GPG签名设置完成！"
    echo "📋 下一步："
    echo "1. 复制上面的公钥"
    echo "2. 在GitHub Settings > SSH and GPG keys 中添加GPG密钥"
    echo "3. 现在所有提交都会自动签名"
else
    echo "❌ 未提供密钥ID，跳过配置"
fi
EOF

chmod +x scripts/setup-commit-signing.sh

# 3. 安全检查脚本
cat > scripts/security-check.sh << 'EOF'
#!/bin/bash

echo "🔍 执行Git安全检查..."

# 检查敏感信息
echo "🔐 检查敏感信息..."
if command -v git-secrets &> /dev/null; then
    git secrets --scan
else
    echo "⚠️ git-secrets未安装，建议安装以检查敏感信息"
    echo "安装命令: brew install git-secrets"
fi

# 检查大文件
echo "📦 检查大文件..."
find . -type f -size +10M -not -path "./.git/*" -not -path "./node_modules/*" | while read file; do
    echo "⚠️ 发现大文件: $file ($(du -h "$file" | cut -f1))"
done

# 检查提交签名
echo "✍️ 检查最近10次提交的签名状态..."
git log --show-signature --oneline -10

# 检查分支保护状态
echo "🛡️ 检查分支保护状态..."
if command -v gh &> /dev/null; then
    gh api repos/:owner/:repo/branches/main/protection 2>/dev/null | jq '.required_status_checks, .enforce_admins, .required_pull_request_reviews' || echo "❌ 无法获取分支保护信息"
else
    echo "⚠️ GitHub CLI未安装，无法检查分支保护状态"
fi

echo "✅ 安全检查完成！"
EOF

chmod +x scripts/security-check.sh

# 4. 创建安全配置文件
cat > .gitattributes << 'EOF'
# Git LFS配置
*.zip filter=lfs diff=lfs merge=lfs -text
*.tar.gz filter=lfs diff=lfs merge=lfs -text
*.pdf filter=lfs diff=lfs merge=lfs -text
*.psd filter=lfs diff=lfs merge=lfs -text
*.ai filter=lfs diff=lfs merge=lfs -text

# 二进制文件
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary

# 行尾符处理
*.js text eol=lf
*.ts text eol=lf
*.jsx text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf

# 敏感文件（永不提交）
.env linguist-generated=true
.env.local linguist-generated=true
.env.*.local linguist-generated=true
*.key linguist-generated=true
*.pem linguist-generated=true
EOF

cat > .gitignore << 'EOF'
# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
dist/
build/
.next/
out/

# 缓存
.cache/
.parcel-cache/
.npm/
.yarn/

# 日志
logs/
*.log

# 运行时
pids/
*.pid
*.seed
*.pid.lock

# 编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.temp/

# 密钥和证书
*.key
*.pem
*.p12
*.crt
*.cer
*.der

# 数据库
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old
EOF

echo "✅ Git安全策略设置完成！"
echo "📋 安全工具："
echo "  ./scripts/setup-commit-signing.sh  # 设置提交签名"
echo "  ./scripts/security-check.sh        # 执行安全检查"
echo "  查看文档: .github/branch-protection.md"
````

## 📋 总结和最佳实践

````markdown path=git-best-practices-summary.md mode=EDIT
# HawaiiHub Git管理最佳实践总结

## 🎯 核心原则

### 1. 仓库架构选择
- **Monorepo**: 适用于共享组件、统一技术栈的项目
- **Multi-repo**: 适用于独立部署、不同技术栈的项目
- **混合模式**: HawaiiHub推荐方案，核心功能Monorepo + 独立服务Multi-repo

### 2. 工作流程选择
- **GitFlow**: 适用于定期发布的大型项目
- **GitHub Flow**: 适用于持续部署的敏捷项目
- **GitLab Flow**: 适用于多环境部署的企业项目

### 3. 分支管理策略
```bash
# 分支命名规范
feature/功能描述          # 新功能开发
bugfix/问题描述           # Bug修复
hotfix/紧急修复描述       # 生产环境紧急修复
release/版本号            # 版本发布准备

# 示例
feature/hawaiihub-ai-chat
bugfix/mobile-responsive-layout
hotfix/security-vulnerability-fix
release/v2.1.0
```

## 🛠️ 实用工具脚本

### 项目初始化
```bash
# 1. 设置Git工作流
./scripts/gitflow-manager.sh

# 2. 配置子模块管理
./scripts/submodule-manager.sh sync

# 3. 设置提交签名
./scripts/setup-commit-signing.sh

# 4. 执行安全检查
./scripts/security-check.sh
```

### 日常开发
```bash
# 开始新功能
./scripts/gitflow-manager.sh feature start ai-enhancement

# 解决冲突
./scripts/conflict-resolver.sh

# 发布版本
./scripts/release-manager.sh minor
```

## 📊 团队协作规范

### 代码审查检查清单
- [ ] 功能是否正确实现
- [ ] 代码是否符合规范
- [ ] 是否有足够的测试
- [ ] 是否有性能问题
- [ ] 是否存在安全漏洞
- [ ] 文档是否更新

### 提交信息规范
```bash
# 格式：类型(范围): 描述
feat(ai): 添加语音识别功能
fix(auth): 修复登录超时问题
docs(readme): 更新安装指南
style(ui): 调整按钮样式
refactor(api): 重构用户服务
test(unit): 添加用户模块测试
chore(deps): 更新依赖版本
```

## 🔐 安全最佳实践

### 1. 分支保护
- 主分支禁止直接推送
- 要求Pull Request审查
- 要求状态检查通过
- 启用管理员强制规则

### 2. 提交签名
- 使用GPG签名所有提交
- 验证提交者身份
- 防止提交历史篡改

### 3. 敏感信息保护
- 使用.gitignore忽略敏感文件
- 配置git-secrets检查
- 定期审查提交历史

## 📈 性能优化

### 1. 仓库大小控制
- 使用Git LFS管理大文件
- 定期清理无用分支
- 避免提交编译产物

### 2. 克隆优化
```bash
# 浅克隆
git clone --depth 1 <repo-url>

# 部分克隆
git clone --filter=blob:none <repo-url>

# 稀疏检出
git sparse-checkout init --cone
git sparse-checkout set apps/hawaiihub-ai
```

## 🚀 自动化集成

### GitHub Actions工作流
- 自动化测试
- 代码质量检查
- 安全扫描
- 自动部署
- 版本发布

### 监控和告警
- 提交频率监控
- 代码质量趋势
- 安全漏洞告警
- 性能回归检测

---

**最后更新**: 2025年1月28日
**维护者**: HawaiiHub开发团队
**版本**: v1.0.0
````

这个完整的企业级Git管理策略涵盖了：

## ✅ 已实现的功能

1. **🏗️ 仓库架构策略** - Monorepo vs Multi-repo对比和混合方案
2. **🔗 Git子模块管理** - 完整的子模块工作流程和管理脚本
3. **🌊 标准工作流程** - GitFlow、GitHub Flow、GitLab Flow详细实现
4. **👥 团队协作规范** - 代码审查、冲突解决、版本发布流程
5. **🔐 安全策略** - 分支保护、提交签名、敏感信息保护

## 🎯 核心特色

- **基于HawaiiHub实际项目** - 所有示例都结合火鸟门户系统和AI助手项目
- **完整的自动化脚本** - 提供可直接使用的管理脚本
- **详细的操作指南** - 每个步骤都有具体的命令示例
- **企业级安全考虑** - 完整的权限控制和安全策略

现在你可以根据项目需求选择合适的策略并使用提供的脚本来实施！
