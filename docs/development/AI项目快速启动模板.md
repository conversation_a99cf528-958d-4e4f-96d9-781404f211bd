# 🚀 AI项目快速启动模板

## 📋 项目创建步骤

### 1. 创建项目仓库

```bash
# 在GitHub上创建新仓库：hawaiihub-ai
# 然后在本地克隆

git clone https://github.com/your-username/hawaiihub-ai.git
cd hawaiihub-ai
```

### 2. 项目结构创建

```bash
# 创建目录结构
mkdir -p app/{models,api,services,utils}
mkdir -p tests
mkdir -p docs
mkdir -p scripts
mkdir -p logs
touch logs/.gitkeep

# 创建核心文件
touch app/__init__.py
touch app/main.py
touch app/config.py
touch tests/__init__.py
touch tests/test_api.py
touch requirements.txt
touch .env.example
touch .gitignore
touch docker-compose.yml
```

## 📄 核心配置文件模板

### requirements.txt
```txt
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库
sqlalchemy==2.0.23
pymysql==1.1.0
redis==5.0.1

# AI相关
openai==1.3.7
langchain==0.0.340
requests==2.31.0

# 工具库
python-dotenv==1.0.0
pydantic==2.5.0
python-multipart==0.0.6

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 日志
loguru==0.7.2

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
```

### .env.example
```env
# 应用配置
APP_NAME=HawaiiHub AI Service
APP_VERSION=1.0.0
DEBUG=True
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/hawaiihub_ai
REDIS_URL=redis://localhost:6379/0

# AI服务配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# HawaiiHub集成配置
HAWAIIHUB_API_URL=https://hawaiihub.net/api
HAWAIIHUB_API_KEY=your_hawaiihub_api_key

# JWT配置
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### .gitignore
```gitignore
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv/

# 环境变量
.env
.env.local
.env.production

# 日志文件
*.log
logs/*.log

# 数据库
*.db
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# 测试
.coverage
htmlcov/
.pytest_cache/

# 部署
dist/
build/
*.egg-info/

# Docker
.dockerignore

# 临时文件
tmp/
temp/
*.tmp
```

### app/main.py
```python
"""
HawaiiHub AI Service 主应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import uvicorn

from app.config import settings
from app.api import ai_router, health_router

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="HawaiiHub AI集成服务",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else ["https://hawaiihub.net"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health_router, prefix="/health", tags=["健康检查"])
app.include_router(ai_router, prefix="/api/v1", tags=["AI服务"])

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info(f"{settings.APP_NAME} v{settings.APP_VERSION} 启动中...")
    logger.info(f"调试模式: {settings.DEBUG}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info(f"{settings.APP_NAME} 正在关闭...")

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
```

### app/config.py
```python
"""
应用配置管理
"""
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """应用设置"""
    
    # 应用基础配置
    APP_NAME: str = "HawaiiHub AI Service"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # AI服务配置
    OPENAI_API_KEY: str
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    
    # HawaiiHub集成配置
    HAWAIIHUB_API_URL: str
    HAWAIIHUB_API_KEY: str
    
    # JWT配置
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局设置实例
settings = Settings()
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:password@db:3306/hawaiihub_ai
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: hawaiihub_ai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "app.main"]
```

## 🔧 开发工具配置

### .vscode/settings.json
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "88"],
    "editor.formatOnSave": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true
    }
}
```

### pytest.ini
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
asyncio_mode = auto
```

## 📚 快速开发指南

### 1. 环境设置
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 复制环境变量文件
cp .env.example .env
# 编辑.env文件，填入实际配置
```

### 2. 数据库初始化
```bash
# 创建数据库（在MySQL中执行）
CREATE DATABASE hawaiihub_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 运行数据库迁移（如果有的话）
python -m app.database.init_db
```

### 3. 启动开发服务器
```bash
# 开发模式启动
python -m app.main

# 或使用uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_api.py

# 运行测试并生成覆盖率报告
pytest --cov=app tests/
```

### 5. API文档访问
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🚀 部署到生产环境

### 使用Docker部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f app
```

### 使用宝塔面板部署
1. 上传代码到服务器
2. 配置Python环境
3. 安装依赖包
4. 配置Nginx反向代理
5. 设置进程守护（PM2或Supervisor）

---

**现在您可以开始创建您的AI项目了！按照这个模板，您将拥有一个结构清晰、规范完整的企业级AI项目。**
