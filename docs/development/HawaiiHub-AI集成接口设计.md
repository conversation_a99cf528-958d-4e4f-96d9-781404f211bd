# 🔗 HawaiiHub-AI集成接口设计

## 📋 集成架构概述

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    HawaiiHub 火鸟门户系统                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   新闻模块   │  │   用户模块   │  │   其他模块   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                           │                                 │
│  ┌─────────────────────────▼─────────────────────────┐     │
│  │              AI集成接口层                        │     │
│  └─────────────────────────┬─────────────────────────┘     │
└────────────────────────────┼─────────────────────────────────┘
                             │ HTTP API
                             │ JSON数据交换
┌────────────────────────────▼─────────────────────────────────┐
│                   HawaiiHub AI 服务                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  内容采集    │  │  智能推荐    │  │  聊天机器人  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  文本分析    │  │  图像处理    │  │  数据挖掘    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🔌 API接口规范

### 基础配置
```yaml
基础信息:
  协议: HTTPS
  格式: JSON
  编码: UTF-8
  认证: API Key + JWT Token
  
端点配置:
  AI服务地址: https://ai.hawaiihub.net
  API版本: v1
  基础路径: /api/v1
```

### 认证机制
```http
# 请求头配置
Authorization: Bearer <JWT_TOKEN>
X-API-Key: <API_KEY>
Content-Type: application/json
Accept: application/json
```

## 📡 核心API接口

### 1. 内容采集接口

#### 新闻采集
```http
POST /api/v1/content/news/collect
```

**请求参数：**
```json
{
  "source": "hawaii_news_now",
  "category": "local",
  "language": "zh-cn",
  "limit": 10,
  "keywords": ["夏威夷", "华人", "社区"]
}
```

**响应格式：**
```json
{
  "status": "success",
  "code": 200,
  "message": "采集成功",
  "data": {
    "total": 5,
    "articles": [
      {
        "id": "news_001",
        "title": "夏威夷华人社区举办春节庆祝活动",
        "summary": "文章摘要...",
        "content": "文章内容...",
        "source": "Hawaii News Now",
        "url": "https://example.com/news/001",
        "published_at": "2025-01-28T10:00:00Z",
        "category": "community",
        "tags": ["春节", "华人", "庆祝"],
        "images": [
          {
            "url": "https://example.com/image1.jpg",
            "caption": "图片说明"
          }
        ]
      }
    ]
  },
  "timestamp": "2025-01-28T12:00:00Z"
}
```

#### 招聘信息采集
```http
POST /api/v1/content/jobs/collect
```

**请求参数：**
```json
{
  "source": "indeed_hawaii",
  "location": "honolulu",
  "keywords": ["中文", "华人", "双语"],
  "job_type": "full_time",
  "limit": 20
}
```

### 2. 智能推荐接口

#### 个性化内容推荐
```http
POST /api/v1/recommendation/content
```

**请求参数：**
```json
{
  "user_id": 12345,
  "content_type": "news",
  "preferences": {
    "categories": ["local", "business", "community"],
    "languages": ["zh-cn", "en"],
    "interests": ["房产", "教育", "美食"]
  },
  "limit": 10
}
```

**响应格式：**
```json
{
  "status": "success",
  "code": 200,
  "data": {
    "recommendations": [
      {
        "content_id": "news_001",
        "title": "推荐内容标题",
        "score": 0.95,
        "reason": "基于您的兴趣：房产、教育",
        "content_type": "news",
        "url": "/news/detail/001"
      }
    ]
  }
}
```

### 3. 聊天机器人接口

#### 智能对话
```http
POST /api/v1/chat/message
```

**请求参数：**
```json
{
  "session_id": "chat_session_001",
  "user_id": 12345,
  "message": "请问夏威夷有哪些好的中文学校？",
  "context": {
    "location": "honolulu",
    "language": "zh-cn"
  }
}
```

**响应格式：**
```json
{
  "status": "success",
  "code": 200,
  "data": {
    "response": "根据我的了解，夏威夷有几所不错的中文学校...",
    "suggestions": [
      "夏威夷中文学校地址查询",
      "中文学校学费信息",
      "报名流程说明"
    ],
    "related_links": [
      {
        "title": "夏威夷中文教育资源",
        "url": "/education/chinese-schools"
      }
    ]
  }
}
```

### 4. 文本分析接口

#### 内容分类
```http
POST /api/v1/analysis/classify
```

**请求参数：**
```json
{
  "text": "要分析的文本内容...",
  "language": "zh-cn",
  "categories": ["news", "job", "housing", "service"]
}
```

**响应格式：**
```json
{
  "status": "success",
  "code": 200,
  "data": {
    "category": "housing",
    "confidence": 0.92,
    "tags": ["租房", "公寓", "夏威夷"],
    "sentiment": "neutral",
    "keywords": ["两室一厅", "月租", "交通便利"]
  }
}
```

## 🔧 火鸟系统集成代码示例

### PHP集成类
```php
<?php
/**
 * HawaiiHub AI服务集成类
 */
class HawaiiHubAI {
    private $apiUrl;
    private $apiKey;
    private $jwtToken;
    
    public function __construct($apiUrl, $apiKey) {
        $this->apiUrl = rtrim($apiUrl, '/');
        $this->apiKey = $apiKey;
        $this->jwtToken = $this->getJwtToken();
    }
    
    /**
     * 获取JWT Token
     */
    private function getJwtToken() {
        $response = $this->makeRequest('POST', '/auth/token', [
            'api_key' => $this->apiKey
        ]);
        
        return $response['data']['token'] ?? null;
    }
    
    /**
     * 采集新闻内容
     */
    public function collectNews($params = []) {
        return $this->makeRequest('POST', '/api/v1/content/news/collect', $params);
    }
    
    /**
     * 获取内容推荐
     */
    public function getRecommendations($userId, $contentType = 'news', $limit = 10) {
        return $this->makeRequest('POST', '/api/v1/recommendation/content', [
            'user_id' => $userId,
            'content_type' => $contentType,
            'limit' => $limit
        ]);
    }
    
    /**
     * 聊天机器人对话
     */
    public function chatMessage($sessionId, $userId, $message, $context = []) {
        return $this->makeRequest('POST', '/api/v1/chat/message', [
            'session_id' => $sessionId,
            'user_id' => $userId,
            'message' => $message,
            'context' => $context
        ]);
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $endpoint, $data = []) {
        $url = $this->apiUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'X-API-Key: ' . $this->apiKey
        ];
        
        if ($this->jwtToken) {
            $headers[] = 'Authorization: Bearer ' . $this->jwtToken;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            throw new Exception('API请求失败');
        }
        
        $result = json_decode($response, true);
        
        if ($httpCode >= 400) {
            throw new Exception($result['message'] ?? 'API请求错误');
        }
        
        return $result;
    }
}

// 使用示例
try {
    $ai = new HawaiiHubAI('https://ai.hawaiihub.net', 'your_api_key');
    
    // 采集新闻
    $news = $ai->collectNews([
        'source' => 'hawaii_news_now',
        'category' => 'local',
        'limit' => 5
    ]);
    
    // 获取推荐内容
    $recommendations = $ai->getRecommendations(12345, 'news', 10);
    
    // 聊天对话
    $chatResponse = $ai->chatMessage('session_001', 12345, '夏威夷天气如何？');
    
} catch (Exception $e) {
    error_log('AI服务调用失败: ' . $e->getMessage());
}
?>
```

### JavaScript集成示例
```javascript
/**
 * HawaiiHub AI服务客户端
 */
class HawaiiHubAIClient {
    constructor(apiUrl, apiKey) {
        this.apiUrl = apiUrl.replace(/\/$/, '');
        this.apiKey = apiKey;
        this.jwtToken = null;
    }
    
    async init() {
        this.jwtToken = await this.getJwtToken();
    }
    
    async getJwtToken() {
        const response = await this.makeRequest('POST', '/auth/token', {
            api_key: this.apiKey
        });
        return response.data.token;
    }
    
    async collectNews(params = {}) {
        return await this.makeRequest('POST', '/api/v1/content/news/collect', params);
    }
    
    async getRecommendations(userId, contentType = 'news', limit = 10) {
        return await this.makeRequest('POST', '/api/v1/recommendation/content', {
            user_id: userId,
            content_type: contentType,
            limit: limit
        });
    }
    
    async chatMessage(sessionId, userId, message, context = {}) {
        return await this.makeRequest('POST', '/api/v1/chat/message', {
            session_id: sessionId,
            user_id: userId,
            message: message,
            context: context
        });
    }
    
    async makeRequest(method, endpoint, data = {}) {
        const url = this.apiUrl + endpoint;
        
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-API-Key': this.apiKey
        };
        
        if (this.jwtToken) {
            headers['Authorization'] = `Bearer ${this.jwtToken}`;
        }
        
        const config = {
            method: method,
            headers: headers
        };
        
        if (method !== 'GET' && Object.keys(data).length > 0) {
            config.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(url, config);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || 'API请求失败');
            }
            
            return result;
        } catch (error) {
            console.error('AI服务调用失败:', error);
            throw error;
        }
    }
}

// 使用示例
(async () => {
    try {
        const ai = new HawaiiHubAIClient('https://ai.hawaiihub.net', 'your_api_key');
        await ai.init();
        
        // 采集新闻
        const news = await ai.collectNews({
            source: 'hawaii_news_now',
            category: 'local',
            limit: 5
        });
        
        console.log('采集到的新闻:', news);
        
    } catch (error) {
        console.error('错误:', error);
    }
})();
```

## 🔒 安全考虑

### API安全措施
1. **HTTPS加密** - 所有API通信使用HTTPS
2. **API密钥认证** - 每个请求需要有效的API密钥
3. **JWT Token** - 使用JWT进行会话管理
4. **请求限制** - 实施API调用频率限制
5. **输入验证** - 严格验证所有输入参数
6. **错误处理** - 不暴露敏感的错误信息

### 数据隐私保护
1. **用户数据加密** - 敏感用户数据加密存储
2. **访问日志** - 记录所有API访问日志
3. **数据脱敏** - 日志中的敏感信息脱敏处理
4. **权限控制** - 基于用户角色的访问控制

## 📈 性能优化

### 缓存策略
1. **Redis缓存** - 热点数据缓存
2. **API响应缓存** - 相同请求结果缓存
3. **数据库查询优化** - 使用索引和查询优化
4. **CDN加速** - 静态资源CDN分发

### 监控指标
1. **响应时间** - API响应时间监控
2. **成功率** - API调用成功率统计
3. **并发量** - 并发请求数量监控
4. **错误率** - 错误请求比例统计

---

**这个集成接口设计为HawaiiHub火鸟门户系统和AI服务提供了完整的通信桥梁，确保两个系统能够高效、安全地协作。**
