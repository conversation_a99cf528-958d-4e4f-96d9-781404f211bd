{"mcpServers": {"github": {"description": "GitHub代码仓库访问 - 搜索代码、查看PR、管理仓库", "command": "npx", "args": ["mcp-remote", "https://github-mcp.vercel.app"]}, "web-search": {"description": "实时网络搜索 - 获取最新技术信息、文档、解决方案", "command": "npx", "args": ["mcp-remote", "https://web-search-mcp.vercel.app"]}, "file-system": {"description": "文件系统操作 - 读取、写入、搜索本地文件", "command": "npx", "args": ["mcp-remote", "https://file-system-mcp.vercel.app"]}, "project-docs": {"description": "项目文档搜索 - 统一搜索所有项目文档", "command": "npx", "args": ["mcp-remote", "https://project-docs-mcp.vercel.app"], "env": {"DOCS_PATH": "/Users/<USER>/Documents/华人平台/生活助手出行助手项目"}}, "hawaiihub-ai": {"description": "夏威夷华人平台AI助手", "command": "npx", "args": ["mcp-remote", "https://mcp.biel.ai/sse?project_slug=hawaiihub&domain=https://hawaiihub.net"]}}}