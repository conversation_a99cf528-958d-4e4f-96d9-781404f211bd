# 🏝️ 夏威夷华人生活助手平台

## 📋 项目概述

本平台基于GitHub开源项目「NextChat」（项目地址：https://github.com/Chanzhaoyu/chatgpt-next-web）二次开发，集成AI对话、地图导航、酒店预订、餐厅推荐等功能，专为夏威夷华人社区打造的综合性生活服务平台。

## 🎯 项目特色

- 🤖 **智能AI对话**：基于GPT-4/Claude的本地化AI助手（api接口）
- 🗺️ **地图导航**：Google Maps集成的智能导航系统（api接口）
- 🏨 **酒店预订**：多平台酒店搜索和预订服务（api接口）
- 🍽️ **餐厅推荐**：檀香山本地美食推荐和预订（api接口）
- 🏖️ **景点服务**：景点介绍和门票预订系统（api接口）
- 📱 **移动优化**：PWA支持，原生应用般体验
- 🌐 **多语言支持**：中文、英文、夏威夷语
- 🔗 **支持扩展**：支持扩展第三方服务(mcp server,api接口)
- 🔗 **用户交互**：文本、语音、图片、视频、文件等多种交互方式
- 🔗 **用户管理**：用户管理、权限管理、角色管理、日志管理
- 🔗 **数据分析**：数据分析、用户画像、行为分析、用户留存分析
- 🔗 **多主题**：多主题支持，支持多种主题，支持自定义主题
- 🔗 **多租户**：多租户支持，支持多个租户独立运行





## 🚀 技术栈

- **前端框架**：React 18 + Next.js 14 + TypeScript
- **状态管理**：Zustand
- **UI组件**：Tailwind CSS + Material-UI
- **地图服务**：Google Maps API
- **AI服务**：OpenAI GPT-4, Anthropic Claude
- **部署平台**：Vercel
- **测试框架**：Jest + React Testing Library
- **代码质量**：ESLint + Prettier + Husky

## 📁 项目结构

```
华人平台-生活助手项目/
├── 📁 src/                     # 源代码目录
│   ├── 📁 app/                 # Next.js应用路由
│   │   ├── 📁 (chat)/         # AI对话模块
│   │   ├── 📁 (map)/          # 地图导航模块
│   │   ├── 📁 (hotels)/       # 酒店预订模块
│   │   ├── 📁 (restaurants)/  # 餐厅推荐模块
│   │   └── 📁 (attractions)/  # 景点服务模块
│   ├── 📁 components/          # React组件库
│   │   ├── 📁 chat/           # 对话相关组件
│   │   ├── 📁 map/            # 地图相关组件
│   │   ├── 📁 hotels/         # 酒店相关组件
│   │   ├── 📁 restaurants/    # 餐厅相关组件
│   │   ├── 📁 attractions/    # 景点相关组件
│   │   └── 📁 ui/             # 基础UI组件
│   ├── 📁 lib/                 # 工具函数库
│   ├── 📁 hooks/               # 自定义Hooks
│   ├── 📁 store/               # 状态管理
│   └── 📁 types/               # TypeScript类型定义
├── 📁 public/                  # 静态资源
├── 📁 docs/                    # 项目文档
│   ├── 📁 architecture/        # 架构设计文档
│   ├── 📁 development/         # 开发文档
│   ├── 📁 deployment/          # 部署文档
│   └── 📁 user-guides/         # 用户指南
├── 📁 tests/                   # 测试文件
│   ├── 📁 unit/               # 单元测试
│   ├── 📁 integration/        # 集成测试
│   └── 📁 e2e/                # 端到端测试
├── 📁 scripts/                 # 构建和部署脚本
├── 📁 config/                  # 配置文件
└── 📁 tools/                   # 开发工具
```

## 🛠️ 开发环境

### 环境要求
- Node.js 18+
- Yarn 1.22.19+
- TypeScript 5.2.2

### 快速开始
```bash
# 克隆项目
git clone [项目地址]

# 安装依赖
cd 华人平台-生活助手项目
yarn install

# 启动开发服务器
yarn dev

# 运行测试
yarn test

# 构建项目
yarn build
```

### 可用脚本
- `yarn dev` - 启动开发服务器
- `yarn build` - 构建生产版本
- `yarn test` - 运行测试
- `yarn lint` - 代码检查
- `yarn hawaii:dev` - 夏威夷主题开发模式
- `yarn hawaii:build` - 夏威夷主题构建

## 📚 文档说明

详细的项目文档位于 `docs/` 目录：

- **docs/architecture/** - 系统架构设计文档
- **docs/development/** - 开发相关文档和指南
- **docs/deployment/** - 部署和运维文档
- **docs/user-guides/** - 用户使用指南

## 🎯 开发目标

### 第一阶段 ✅ 已完成
- 项目架构搭建
- 基础组件开发
- 开发环境配置
- 类型定义系统

### 第二阶段 🔄 进行中
- AI对话功能扩展
- 多语言支持（中文、英文、夏威夷语）
- 本地知识库集成
- 语音输入和播报功能

### 第三阶段 📋 计划中
- 地图导航功能
- 酒店预订系统
- 餐厅推荐服务
- 景点门票预订

## 🤝 贡献指南

欢迎为檀香山生活助手项目做出贡献！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目主页：[项目地址]
- 问题反馈：[Issues链接]
- 邮箱：[联系邮箱]

---

**檀香山生活助手** - 让夏威夷华人生活更美好！🏝️ 