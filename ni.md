# OpenAI GPT Actions 大白话教学

## 什么是 GPT Actions？

**GPT Actions** 是让 ChatGPT 能够"动手做事"的功能。它存储在[自定义GPT](https://openai.com/blog/introducing-gpts)中，让用户能够通过提供指令、附加文档作为知识，并连接到第三方服务来自定义ChatGPT。

### 大白话解释

想象一下：
- **普通 ChatGPT**：就像一个很聪明的朋友，只能聊天回答问题
- **带 Actions 的 ChatGPT**：就像一个既聪明又能干的助手，不仅能聊天，还能帮你发邮件、查天气、管理文件、创建任务等等

## 核心原理

GPT Actions 的核心就是**函数调用（Function Calling）**。简单理解：

1. **用户说人话**："帮我查一下明天北京的天气"
2. **ChatGPT 翻译**：自动转换成API调用的格式
3. **执行操作**：调用天气API获取数据
4. **返回结果**：用自然语言告诉你结果

### 技术工作原理

GPT Actions 利用[函数调用](https://platform.openai.com/docs/guides/function-calling)来执行API调用：

1. **决定调用哪个API**：根据用户问题选择相关的API端点
2. **生成JSON输入**：将自然语言转换为API调用所需的JSON格式
3. **执行API调用**：使用生成的JSON数据调用第三方服务
4. **返回自然语言结果**：将API响应转换为用户友好的自然语言

开发者可以指定操作的认证机制，自定义GPT将使用第三方应用的认证来执行API调用。

## GPT Actions 的强大之处

### 解决的核心问题
- **互操作性**：让组织能够访问其他应用程序
- **降低开发成本**：开发者只需描述API调用的模式，配置认证，添加指令
- **自然语言桥接**：ChatGPT提供用户自然语言问题与API层之间的桥梁

## 实际创建步骤

### 示例1：天气查询GPT

#### 步骤1：创建GPT并添加指令

1. 在左侧导航栏点击"Explore GPTs"
2. 点击右上角的"+ Create"
3. 跳转到GPT编辑器的_Configure_标签页
4. 给GPT起名和描述

**指令示例：**
```
你提供特定位置的最新天气信息。
当用户询问特定位置的天气时，获取该位置的坐标（纬度、经度），并调用open-meteo端点。
除非另有说明，否则temperature_unit将为华氏度。
如果用户请求多个位置，请将所有纬度用逗号分隔传递，经度也按相同顺序传递。

示例：
- 用户：BYU Hawaii的天气如何？
  GPT：latitude=21.6413&longitude=157.9251
- 用户：盐湖城和德国不来梅的天气如何？
  GPT：latitude=40.7608,53.0793&longitude=111.8910,8.8017
```

#### 步骤2：创建Action

1. 在_Configure_标签页底部的"Actions"下，点击"Create new action"
2. 跳过"Authentication"部分（此API不需要认证）
3. 在"Schema"部分粘贴OpenAPI规范

**OpenAPI Schema示例：**
```yaml
openapi: 3.1.0
info:
  title: Open-Meteo Weather Forecast API
  description: |
    Open-Meteo provides free weather forecast APIs for specific latitude and longitude coordinates. 
    This specification covers the forecast endpoint.
  version: 1.0.0
servers:
  - url: https://api.open-meteo.com/v1
    description: Main Open-Meteo API server
paths:
  /forecast:
    get:
      operationId: getWeatherForecast
      summary: Get weather forecast for a specific location
      description: |
        Retrieves weather forecast data for the given latitude and longitude.
      parameters:
        - name: latitude
          in: query
          required: true
          description: Latitude of the location
          schema:
            type: number
            format: float
        - name: longitude
          in: query
          required: true
          description: Longitude of the location
          schema:
            type: number
            format: float
        - name: current_weather
          in: query
          required: false
          description: Include current weather data (true or false)
          schema:
            type: boolean
      responses:
        '200':
          description: Successful weather forecast response
          content:
            application/json:
              schema:
                type: object
                properties:
                  latitude:
                    type: number
                  longitude:
                    type: number
                  current_weather:
                    type: object
                    nullable: true
                    properties:
                      temperature:
                        type: number
                      windspeed:
                        type: number
                      winddirection:
                        type: number
                      weathercode:
                        type: integer
                      time:
                        type: string
```

#### 步骤3：测试和优化

在GPT的_Preview_视图中测试GPT，发送提示并观察响应。

### 示例2：Google日历助手GPT

#### 前置条件：启用Google日历API

在创建连接到Google日历的GPT之前，需要启用API。访问GPT Actions库中的Google日历教程并按照"Google日历配置步骤"中的说明操作。

#### 步骤1：创建GPT

创建一个GPT并给它起名，例如"Google Calendar Assistant GPT"。

#### 步骤2：创建Action

1. 点击_Configure_视图底部的"Create new action"
2. 点击"Authentication"下的齿轮图标，选择"OAuth"
3. 从Google Cloud Console输入Client ID和Client Secret
4. 输入以下详细信息：
   - Authorization URL: https://accounts.google.com/o/oauth2/auth
   - Token URL: https://oauth2.googleapis.com/token
   - Scopes: https://www.googleapis.com/auth/calendar
5. 保存

#### 步骤3：添加OpenAPI Schema

```yaml
openapi: 3.1.0
info:
  title: Google Calendar API
  description: This API allows you to read and create events in a user's Google Calendar.
  version: 1.0.0
servers:
  - url: https://www.googleapis.com/calendar/v3
    description: Google Calendar API server
 
paths:
  /calendars/primary/events:
    get:
      summary: List events from the primary calendar
      description: Retrieve a list of events from the user's primary Google Calendar.
      operationId: listEvents
      tags:
        - Calendar
      parameters:
        - name: timeMin
          in: query
          description: The lower bound (inclusive) of the events to retrieve, in RFC3339 format.
          required: false
          schema:
            type: string
            format: date-time
        - name: timeMax
          in: query
          description: The upper bound (exclusive) of the events to retrieve, in RFC3339 format.
          required: false
          schema:
            type: string
            format: date-time
        - name: maxResults
          in: query
          description: The maximum number of events to return.
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: A list of events
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          description: The event ID
                        summary:
                          type: string
                          description: The event summary (title)
                        start:
                          type: object
                          properties:
                            dateTime:
                              type: string
                              format: date-time
                              description: The start time of the event
                        end:
                          type: object
                          properties:
                            dateTime:
                              type: string
                              format: date-time
                              description: The end time of the event
                        location:
                          type: string
                          description: The location of the event
                        description:
                          type: string
                          description: A description of the event
 
    post:
      summary: Create a new event on the primary calendar
      description: Creates a new event on the user's primary Google Calendar.
      operationId: createEvent
      tags:
        - Calendar
      requestBody:
        description: The event data to create.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                summary:
                  type: string
                  description: The title of the event
                  example: "Team Meeting"
                location:
                  type: string
                  description: The location of the event
                  example: "Conference Room 1"
                description:
                  type: string
                  description: A detailed description of the event
                  example: "Discuss quarterly results"
                start:
                  type: object
                  properties:
                    dateTime:
                      type: string
                      format: date-time
                      description: Start time of the event
                      example: "2024-11-30T09:00:00Z"
                    timeZone:
                      type: string
                      description: Time zone of the event start
                      example: "UTC"
                end:
                  type: object
                  properties:
                    dateTime:
                      type: string
                      format: date-time
                      description: End time of the event
                      example: "2024-11-30T10:00:00Z"
                    timeZone:
                      type: string
                      description: Time zone of the event end
                      example: "UTC"
              required:
                - summary
                - start
                - end
      responses:
        '201':
          description: Event created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: The ID of the created event
                  summary:
                    type: string
                    description: The event summary (title)
                  start:
                    type: object
                    properties:
                      dateTime:
                        type: string
                        format: date-time
                        description: The start time of the event
                  end:
                    type: object
                    properties:
                      dateTime:
                        type: string
                        format: date-time
                        description: The end time of the event
```

## 实际应用场景

### 1. 生产力工具
- **Google Drive**：直接让 ChatGPT 帮你查找、创建、编辑文档
- **GitHub**：让 ChatGPT 帮你查看代码、创建仓库、管理项目
- **Box**：文件管理和协作
- **Workday**：人力资源和财务管理

### 2. 数据和分析
- **Snowflake**：直接查询数据仓库
- **BigQuery**：Google 云数据分析
- **AWS Redshift**：亚马逊数据仓库
- **SQL Database**：各种数据库操作

### 3. 沟通工具
- **Gmail**：让 ChatGPT 帮你发邮件、管理收件箱
- **Outlook**：微软邮箱管理
- **Google Calendar**：日程安排和会议管理

### 4. 项目管理
- **Salesforce**：客户关系管理
- **Jira**：项目任务管理
- **Notion**：知识管理和协作
- **Confluence**：团队文档管理

## 两种实现方式

### 1. 直接集成（第三方 Actions）
直接连接到第三方服务的API，比如：
- 直接连接 Google Drive API
- 直接连接 Snowflake 数据库
- 直接连接 GitHub API

**优点**：简单直接，响应快
**缺点**：功能相对有限

### 2. 中间件集成（Middleware Actions）
通过中间件来处理复杂的逻辑，比如：
- **Azure Functions**：微软云函数
- **Google Cloud Functions**：谷歌云函数  
- **AWS Lambda**：亚马逊云函数
- **Zapier**：自动化平台

**优点**：功能强大，可以处理复杂逻辑
**缺点**：设置相对复杂

## 实际例子：天气预报

让我们看一个简单的例子：

**用户问**："我周末去华盛顿DC，应该带什么？"

**GPT Actions 的工作流程**：
1. 理解用户需求：需要查询华盛顿DC的天气
2. 调用第一个API：获取华盛顿DC的地理坐标
3. 调用第二个API：根据坐标获取天气预报
4. 分析天气数据：生成打包建议
5. 返回结果："根据天气预报，建议带雨伞和轻便外套..."

### API调用示例

**/points API调用：**
```json
{
  "latitude": 38.9072,
  "longitude": -77.0369
}
```

**/forecast API调用：**
```json
{
  "wfo": "LWX",
  "x": 97,
  "y": 71
}
```

## 开发者角度

### 设置步骤
1. **描述API**：告诉 ChatGPT 这个API能做什么
2. **配置认证**：设置如何登录第三方服务
3. **写说明**：告诉 ChatGPT 什么时候使用这个功能
4. **测试**：确保一切正常工作

### 需要的技能
- 基本的API调用知识
- 了解JSON格式
- 熟悉第三方服务的认证方式
- 会写简单的说明文档

## 用户角度

### 使用体验
- **无需学习**：直接用自然语言描述需求
- **智能理解**：ChatGPT 自动理解你的意图
- **自动执行**：无需手动操作其他应用
- **自然回复**：结果用自然语言呈现

### 常见用途
- 数据查询："帮我查一下上个月的销售数据"
- 文件操作："帮我创建一个项目计划文档"
- 任务管理："帮我创建一个明天上午的会议"
- 邮件处理："帮我回复这封邮件"

## 注意事项

### 安全考虑
- 需要授权第三方应用访问你的数据
- 确保使用可信的GPT Actions
- 注意数据隐私保护

### 使用限制
- 需要网络连接
- 某些功能可能需要付费账户
- 不是所有应用都支持GPT Actions

### 企业环境注意事项
- 如果使用企业管理的ChatGPT许可证，可能需要管理员启用GPT Actions
- 可能需要将感兴趣服务的域名添加到批准列表中
- 对于CES ChatGPT Edu组织的用户，请联系********************寻求帮助

## 未来展望

GPT Actions 正在快速发展，未来可能会有：
- 更多第三方应用支持
- 更智能的自动化功能
- 更简单的设置流程
- 更强大的数据处理能力

## 总结

GPT Actions 让 ChatGPT 从"只会说话"变成了"能说会做"的智能助手。它大大简化了用户与各种应用程序的交互，让复杂的操作变得像聊天一样简单。

对于开发者来说，它提供了一个简单的方式来为 ChatGPT 添加新功能；对于用户来说，它让日常工作和生活变得更加便捷高效。

---

*参考链接：*
- [OpenAI Cookbook - ChatGPT](https://cookbook.openai.com/topic/chatgpt)
- [GPT Actions Library](https://platform.openai.com/docs/actions/actions-library)  
- [GPT Actions Introduction](https://platform.openai.com/docs/actions/introduction)
- [OpenAI Agents SDK](https://openai.github.io/openai-agents-python/)
- [BYU Creating GPT Actions](https://genai.byu.edu/creating-gpt-actions)
